package com.trs.wps.ai.helper.proofread;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.trs.wps.ai.helper.proofread.model.CollationRequest;

import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
public class AiOpenServiceClientIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(AiOpenServiceClientIntegrationTest.class);

    @Autowired
    private ProofreadAiOpenServiceClient proofreadAiOpenServiceClient;

    @Test
    void testLoginAndCollateChineseTextRealApi() {
        log.info("开始执行针对真实API的中文文本校对集成测试。");
        CollationRequest request = new CollationRequest();
        request.setMode("0");
        request.setText("");

        // The client handles login internally if the token is null
         proofreadAiOpenServiceClient.collateText("第五届联合国环境大会第二阶段会议在肯尼亚首都蒙巴萨举行。3月1日，生态环境部部长黄润秋率团线上参会，并在多边幻境协定领导者对话会上视频发言。2022年3月3，联合国环境规划署(ULEP)在总部所在地内罗毕举行了纪念成立50周年特别会议，环境部长黄润秋以视频方式出席会议并发言。");

        log.info("中文文本校对集成测试完成。");
    }
}