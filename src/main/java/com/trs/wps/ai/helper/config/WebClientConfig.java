package com.trs.wps.ai.helper.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.http.client.HttpClientRequest;

/**
 * @program: wps-ai-helper
 * @description: WebClientConfig
 * @author: zheng.huiyuan
 * @create: 2025-07-17 14:39
 **/
@Configuration
public class WebClientConfig {
    @Autowired
    private LLMClientProperties llmClientProperties;

    @Autowired
    private AiOpenServiceProperties aiOpenServiceProperties;

    @Bean
    public WebClient llmWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, llmClientProperties.getConnectTimeoutMs())
                // ReadTimeoutHandler only fires after headers; for streaming we usually omit or
                // set large
                .doOnConnected(conn -> {
                    if (llmClientProperties.getReadTimeoutMs() != null && llmClientProperties.getReadTimeoutMs() > 0) {
                        conn.addHandlerLast(new ReadTimeoutHandler(llmClientProperties.getReadTimeoutMs() / 1000));
                    }
                });

        return WebClient.builder()
                .baseUrl(llmClientProperties.getBaseUrl())
                // enlarge buffer in case of long markdown chunks
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(cfg -> cfg.defaultCodecs().maxInMemorySize(16 * 1024 * 1024))
                        .build())
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    @Bean
    public WebClient aiOpenServiceWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000) // Using a default timeout, can be externalized if
                                                                    // needed
                .doOnConnected(conn -> {
                    // No read timeout set specifically for this client, as per current requirement.
                    // Adjust if needed.
                });

        return WebClient.builder()
                .baseUrl(aiOpenServiceProperties.getBaseUrl())
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(cfg -> cfg.defaultCodecs().maxInMemorySize(16 * 1024 * 1024))
                        .build())
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }
}
