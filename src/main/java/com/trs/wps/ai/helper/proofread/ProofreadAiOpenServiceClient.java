package com.trs.wps.ai.helper.proofread;

import com.trs.wps.ai.helper.proofread.model.CollationRequest;
import com.trs.wps.ai.helper.proofread.model.CollationResponse;
import com.trs.wps.ai.helper.proofread.model.CollationResult;
import com.trs.wps.ai.helper.proofread.model.LoginRequest;
import com.trs.wps.ai.helper.proofread.model.LoginResponse;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.trs.wps.ai.helper.config.AiOpenServiceProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import com.trs.wps.ai.helper.util.JsonUtils;

/**
 * AI开放服务平台客户端，用于与其API进行交互。
 * 该客户端处理令牌获取和文本校对功能。
 */
@Component
public class ProofreadAiOpenServiceClient {

    private static final Logger log = LoggerFactory.getLogger(ProofreadAiOpenServiceClient.class);

    private final WebClient webClient;
    private final AiOpenServiceProperties properties;
    private String token;
    private long tokenExpiryTime; // Token 的过期时间戳
    private final Duration TOKEN_LIFETIME = Duration.ofMinutes(15); // 定义 Token 有效期为 15 分钟
    private final OkHttpClient okHttpClient;

    /**
     * 构造AiOpenServiceClient实例。
     *
     * @param webClient  专门为AI开放服务平台配置的WebClient实例。
     * @param properties AI开放服务平台的配置属性。
     */
    public ProofreadAiOpenServiceClient(@Qualifier("aiOpenServiceWebClient") WebClient webClient,
            AiOpenServiceProperties properties) {
        this.webClient = webClient;
        this.properties = properties;
        this.okHttpClient = new OkHttpClient();
    }

    /**
     * 登录AI开放服务平台以获取认证令牌。
     * 对应 接口文档.md 中 "5、登录接口" 部分。
     *
     * @return 成功登录后发出获取到的令牌字符串的Mono。
     * @throws RuntimeException 如果登录失败。
     */
    public Mono<String> login() {
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsername(properties.getUsername());
        loginRequest.setCode(properties.getCode());

        log.info("尝试登录AI开放服务平台。");

        return webClient.post()
                .uri("/ckm-open/api/login")
                .body(BodyInserters.fromValue(loginRequest))
                .retrieve()
                .bodyToMono(LoginResponse.class)
                .map(response -> {
                    if (response.getCode() == 200) {
                        this.token = response.getToken();
                        // 成功获取Token后，计算并设置过期时间
                        this.tokenExpiryTime = System.currentTimeMillis() + TOKEN_LIFETIME.toMillis();
                        log.info("成功登录并获取到令牌。令牌有效期至：{}", new java.util.Date(this.tokenExpiryTime));
                        return response.getToken();
                    } else {
                        String errorMessage = "登录失败: " + response.getMsg();
                        log.error(errorMessage);
                        throw new RuntimeException(errorMessage);
                    }
                })
                .doOnError(e -> log.error("登录错误: {}", e.getMessage()));
    }

    /**
     * 确保存在有效的认证令牌。如果当前令牌无效或即将过期，则尝试重新登录。
     * 包含最多3次的登录重试逻辑。
     * 
     * @return 包含有效令牌字符串的Mono。
     */
    private Mono<String> ensureTokenValid() {
        // 检查当前令牌是否有效且未过期
        if (this.token != null && System.currentTimeMillis() < this.tokenExpiryTime) {
            log.debug("当前令牌有效，无需重新获取。");
            return Mono.just(this.token);
        }

        log.info("令牌无效或已过期，尝试重新登录以获取新令牌。");

        final int MAX_RETRIES = 3;
        return Mono.defer(() -> login()) // defer 确保每次订阅时才执行 login()
                .doOnError(e -> log.warn("获取令牌失败: {}", e.getMessage())) // 记录每次失败
                .retry(MAX_RETRIES) // 使用简单的 retry(times) 实现重试
                .onErrorResume(RuntimeException.class, e -> { // 捕获最终的重试失败异常
                    log.error("经过多次重试后，仍然无法获取有效令牌。原因: {}", e.getMessage());
                    return Mono.error(new RuntimeException("无法获取有效令牌，请检查认证配置。", e));
                })
                .doOnSuccess(t -> log.info("成功获取到新令牌。"));
    }

    /**
     * 使用AI开放服务平台API执行文本校对。
     * 对应 接口文档.md 中 "6、文本校对接口" 部分。
     *
     *   校对模式（例如: "0" 表示综合模式）。
     * @param text 待校对文本。
     * @return 成功校对后发出CollationResult对象列表的Mono。
     * @throws RuntimeException 如果文本校对失败。
     */
    public Mono<List<CollationResult>> collateText(String text) {
        return ensureTokenValid() // 首先确保Token有效
                .flatMap(validToken -> performCollationInternalOkHttp(text)); // 然后执行校对操作
    }

    /**
     * 执行实际文本校对API调用的内部方法，使用OkHttp。
     * 假设令牌已可用。
     *
     *  校对模式。
     * @param text 待校对文本。
     * @return 发出CollationResult对象列表的Mono。
     */
    private Mono<List<CollationResult>> performCollationInternalOkHttp(String text) {
        CollationRequest collationRequest = new CollationRequest();
        collationRequest.setMode("0");
        collationRequest.setText(text);
        log.info("尝试进行文本校对 (OkHttp)。");

        return Mono.fromCallable(() -> {
            MediaType JSON = MediaType.get("application/json; charset=utf-8");
            String requestBody = com.alibaba.fastjson2.JSON.toJSONString(collationRequest);
            log.info("输入的请求参数：{}", requestBody);
            Request request = new Request.Builder()
                    .url(properties.getBaseUrl() + "/ckm-open/api/open/JsonBody/ckm-collate/proxy/collate/text")
                    .header("Authorization", "Bearer " + this.token)
                    .post(RequestBody.create(requestBody, JSON))
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    CollationResponse collationResponse = com.alibaba.fastjson2.JSON.parseObject(responseBody,
                            CollationResponse.class);

                    if (collationResponse.getCode() == 200) {
                        log.info("文本校对成功 (OkHttp)。");
                        log.info("文本校对结果:{}", responseBody);
                        return collationResponse.getData();
                    } else {
                        String errorMessage = "文本校对失败 (OkHttp): " + collationResponse.getMsg();
                        log.error(errorMessage);
                        throw new RuntimeException(errorMessage);
                    }
                } else {
                    String errorMessage = "文本校对请求失败 (OkHttp): " + response.code() + " " + response.message();
                    log.error(errorMessage);
                    throw new RuntimeException(errorMessage);
                }
            }
        }).doOnError(e -> log.error("文本校对错误 (OkHttp): {}", e.getMessage()));
    }

    /**
     * Gets the current authentication token.
     *
     * @return The current token string.
     */
    public String getToken() {
        return token;
    }

    /**
     * Sets the authentication token. This method is primarily for testing purposes.
     *
     * @param token The token string to set.
     */
    public void setToken(String token) {
        this.token = token;
    }
}