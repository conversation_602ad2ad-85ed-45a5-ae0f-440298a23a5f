package com.trs.wps.ai.helper.service;

import com.trs.wps.ai.helper.vo.ChatResponseVO;
import com.trs.wps.ai.helper.vo.QnaAnswerEvent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @program: wps-ai-helper
 * @description:
 * @author: zheng.hui<PERSON>
 * @create: 2025-07-17 15:17
 **/
public interface QaService {
    public Mono<ChatResponseVO.ChatData> sendQnaQuestion(String conversationId, String content, String select, String type);

    Flux<QnaAnswerEvent> getStreamAnswer(ChatResponseVO.ChatData chatData, String type);
}
