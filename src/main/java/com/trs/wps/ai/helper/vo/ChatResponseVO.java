package com.trs.wps.ai.helper.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 聊天响应结果
 *
 * <AUTHOR>
 * @create 2025-03-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponseVO {
    
    private Integer code;
    private String msg;
    private List<ChatData> data;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChatData {
        /**
         * 聊天日志ID
         */
        private Long chatLogId;
        
        /**
         * 问题内容
         */
        private String question;
        
        /**
         * 问答ID
         */
        private String qaId;
        
        /**
         * 会话ID
         */
        private String conversationId;
        
        /**
         * 倾向性
         */
        private String tendency;
        
        /**
         * 倾向性原因
         */
        private String tendencyReason;
        
        /**
         * 附件列表
         */
        private List<Appendix> appendixes;

        @Data
        public static class Appendix{
            private Long id;
            private String name;
            private String url;
            private String size;
            private String crTime;
            private boolean valid;
            private String originalName;
        }
        
        /**
         * 是否清除上下文
         */
        private Integer isClearContext;
        
        /**
         * 计算能力是否不足
         */
        private Integer isComputationCapacityLess;
        
        /**
         * 是否询问
         */
        private Boolean ask;
        
        /**
         * 是否敏感
         */
        private Integer isSensitive;
        
        /**
         * 原始问题
         */
        private String originQuestion;
        
        /**
         * 结果
         */
        private Object result;
    }
}