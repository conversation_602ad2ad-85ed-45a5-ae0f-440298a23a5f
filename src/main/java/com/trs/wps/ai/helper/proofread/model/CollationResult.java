package com.trs.wps.ai.helper.proofread.model;

import lombok.Data;
import java.util.List;

/**
 * 文本校对接口响应中，表示单条校对结果。
 * 对应 接口文档.md 中 "6、文本校对接口" 部分的 "data" 数组。
 */
@Data
public class CollationResult {
    /**
     * 原始句子。
     */
    private String sentence;
    /**
     * 句子索引。
     */
    private Integer senIdx;
    /**
     * 错误在句子的起始位置。
     */
    private Integer senStartPos;
    /**
     * 错误在句子的结束位置。
     */
    private Integer senEndPos;
    /**
     * 错误类型编码。
     */
    private Integer errorType;
    /**
     * 错误类型信息（例如: "首都校对"）。
     */
    private String errorTypeInfo;
    /**
     * 错误词。
     */
    private String errorWord;
    /**
     * 错误起始位置。
     */
    private Integer startPos;
    /**
     * 错误结束位置。
     */
    private Integer endPos;
    /**
     * 建议类型（0:可修改, 1:仅提示）。
     */
    private Integer suggestType;
    /**
     * 修改提示或建议列表。
     */
    private List<Suggestion> suggestions;
    /**
     * 合并后的修改建议。
     */
    private String collateWord;
    /**
     * 合并建议的权重。
     */
    private Double weight;
    /**
     * 引擎类型（col:规则引擎, dl:深度学习引擎）。
     */
    private String engine;
}