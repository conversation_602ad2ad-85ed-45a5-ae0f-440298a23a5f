package com.trs.wps.ai.helper.controller;

import com.trs.wps.ai.helper.domain.AnswerChunk;
import com.trs.wps.ai.helper.domain.InferenceStepEnd;
import com.trs.wps.ai.helper.domain.InferenceStepStart;
import com.trs.wps.ai.helper.domain.InferenceStepUpdate;
import com.trs.wps.ai.helper.dto.DocBodyDTO;
import com.trs.wps.ai.helper.proofread.ProofreadAiOpenServiceClient;
import com.trs.wps.ai.helper.proofread.model.CollationRequest;
import com.trs.wps.ai.helper.service.QaService;
import com.trs.wps.ai.helper.util.JsonUtils;
import com.trs.wps.ai.helper.vo.ChatResponseVO;
import com.trs.wps.ai.helper.vo.QnaAnswerEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.http.HttpStatus;

import java.util.UUID;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: ai-helper
 * @description:
 * @author: zheng.huiyuan
 * @create: 2025-07-17 18:01
 **/
@RestController
@Slf4j
public class QaController {

    @Autowired
    private QaService qaService;

    @Autowired
    private ProofreadAiOpenServiceClient proofreadAiOpenServiceClient;

    @Value("${tts.url}")
    private String ttsUrl;

    @PostMapping(value = "/qa", produces = "text/event-stream;charset=utf-8")
    public Flux<String> getStreamAnswer(@RequestBody DocBodyDTO dto) {
        String conversationId = UUID.randomUUID().toString();

        // 1. 发送连接成功和开始推理的事件
        ConnectionInfo connectionInfo = new ConnectionInfo();
        connectionInfo.setMessage("连接成功");
        connectionInfo.setTimestamp(String.valueOf(System.currentTimeMillis()));

        return Flux.just(
                "event: connected\ndata: " + JsonUtils.toJson(connectionInfo) + "\n\n",
                "event: answer_start\ndata: {}")
                // 2. 异步执行问答流程
                .concatWith(Flux.defer(() -> {
                    // 首先发送问题到QA服务获取会话数据
                    return qaService.sendQnaQuestion(conversationId, dto.getContent(), dto.getSelect(), dto.getType())
                            .flatMapMany(chatData -> {
                                // 获取流式回答
                                return qaService.getStreamAnswer(chatData, dto.getType())
                                        .flatMap(event -> {
                                            // 处理不同类型的回答事件
                                            if (event.getUuid() != null) {
                                                // 这是一个分析步骤的事件
                                                String stepId = event.getUuid();
                                                String stepName = "";

                                                // 根据事件类型处理步骤
                                                if ("INTENTION_RECOGNIZE".equals(event.getBizType())) {
                                                    stepName = "理解意图";
                                                } else if ("EXECUTE_INSTRUCTION".equals(event.getBizType())) {
                                                    stepName = "查找数据";
                                                } else if ("GENERATE_ANSWER".equals(event.getBizType())) {
                                                    stepName = "阅读数据";
                                                }

                                                // 根据事件状态返回对应的SSE事件
                                                if (event.getNlpBegin() != null && event.getNlpBegin()) {
                                                    // 步骤开始
                                                    InferenceStepStart start = new InferenceStepStart();
                                                    start.setStepId(stepId);
                                                    start.setStepName(stepName);
                                                    start.setStatus("running");
                                                    return Mono.just("event: inference_step_start\ndata: " +
                                                            JsonUtils.toJson(start) + "\n\n");
                                                } else if (event.getIsEnd() != null && event.getIsEnd() == 1) {
                                                    // 步骤结束
                                                    InferenceStepEnd end = new InferenceStepEnd();
                                                    end.setStepId(stepId);
                                                    end.setStepName(stepName);
                                                    end.setStatus("completed");
                                                    return Mono.just("event: inference_step_end\ndata: " +
                                                            JsonUtils.toJson(end) + "\n\n");
                                                } else if (event.getReasoningContent() != null) {
                                                    // 步骤更新
                                                    InferenceStepUpdate update = new InferenceStepUpdate();
                                                    update.setStepId(stepId);
                                                    update.setUpdateContent(event.getReasoningContent());
                                                    update.setModel("newline");
                                                    return Mono.just("event: inference_step_update\ndata: " +
                                                            JsonUtils.toJson(update) + "\n\n");
                                                } else if (event.getIsEnd() != null && event.getIsEnd() == 1
                                                        || (event.getStages() != null && !event.getStages().isEmpty()
                                                                && event.getStages().get(0).getStatus()
                                                                        .equals("DONE"))) {
                                                    log.info("结束");
                                                    // 整个回答结束
                                                    return Mono.just("event: answer_end\ndata: {}\n\n");
                                                }
                                            } else if (event.getAnswer() != null) {

                                                if (event.getIsEnd() != null && event.getIsEnd() == 1) {
                                                    log.info("结束");
                                                    // 整个回答结束
                                                    return Mono.just("event: answer_end\ndata: {}\n\n");
                                                }
                                                // 这是回答文本
                                                AnswerChunk chunk = new AnswerChunk();
                                                chunk.setText(event.getAnswer());
                                                chunk.setIndex(System.currentTimeMillis());
                                                return Mono.just("event: answer_chunk\ndata: " +
                                                        JsonUtils.toJson(chunk) + "\n\n");
                                            } else if (event.getIsEnd() != null && event.getIsEnd() == 1
                                                    || (event.getStages() != null && !event.getStages().isEmpty()
                                                            && event.getStages().get(0).getStatus().equals("DONE"))) {
                                                log.info("结束");
                                                // 整个回答结束
                                                return Mono.just("event: answer_end\ndata: {}\n\n");
                                            }

                                            // 其他情况，忽略事件
                                            return Mono.empty();
                                        });
                            })
                            .subscribeOn(Schedulers.boundedElastic())
                            .onErrorResume(e -> {
                                log.error("处理回答流失败", e);
                                return Flux.just("event: error\ndata: 获取回答失败，请重试\n\n");
                            });
                }));
    }

    @PostMapping(value = "/proofread", produces = "text/event-stream;charset=utf-8")
    public Flux<String> getStreamProofread(@RequestBody String text) {
        return proofreadAiOpenServiceClient.collateText(text)
                .flatMapMany(results -> Flux.fromIterable(results)
                        .map(result -> "event: proofread_result\ndata: " + JsonUtils.toJson(result) + "\n\n"))
                .onErrorResume(e -> {
                    log.error("校对文本失败", e);
                    return Flux.just("event: error\ndata: 校对失败，请重试\n\n");
                });
    }

    @PostMapping(value = "/tts", produces = "audio/pcm")
    public Flux<byte[]> pushTextToTTS(@RequestBody String text) {
        // 使用Map构建请求体，避免JSON格式和转义问题
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("text", text);
        requestMap.put("speaker", "客服001");
        requestMap.put("instruct", "");
        requestMap.put("response_format", "pcm");
        requestMap.put("enable_stream", true);

        // 使用WebClient发送请求并处理流式响应
        return WebClient.create()
                .post()
                .uri(ttsUrl)
                .header("Content-Type", "application/json")
                .accept(MediaType.valueOf("audio/pcm"))
                .bodyValue(requestMap)
                .retrieve()
                .onStatus(status -> status.is4xxClientError(), clientResponse -> {
                    log.error("TTS API客户端错误: {}", clientResponse.statusCode());
                    return clientResponse.bodyToMono(String.class)
                            .flatMap(errorBody -> {
                                log.error("TTS错误响应详情: {}", errorBody);
                                return Mono.error(new RuntimeException("TTS服务调用失败: " + clientResponse.statusCode()));
                            });
                })
                .onStatus(status -> status.is5xxServerError(), clientResponse -> {
                    log.error("TTS API服务器错误: {}", clientResponse.statusCode());
                    return Mono.error(new RuntimeException("TTS服务器错误: " + clientResponse.statusCode()));
                })
                .bodyToFlux(byte[].class)
                .onErrorResume(e -> {
                    log.error("TTS流式转换失败", e);
                    return Flux.empty();
                });
    }

    // 连接信息内部类
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ConnectionInfo {
        private String message;
        private String timestamp;
    }
}
