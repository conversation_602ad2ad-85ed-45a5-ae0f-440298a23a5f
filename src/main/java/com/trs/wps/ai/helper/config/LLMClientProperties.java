package com.trs.wps.ai.helper.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @program: wps-ai-helper
 * @description:
 * @author: zheng.huiyuan
 * @create: 2025-07-17 14:26
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "helper.llm")
public class LLMClientProperties {
    private String baseUrl;
    private String tenantId;
    private String username;
    private String accessToken;
    private Integer connectTimeoutMs = 5000;
    private Integer readTimeoutMs = 0;
}
