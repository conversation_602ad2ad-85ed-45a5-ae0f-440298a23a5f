package com.trs.wps.ai.helper.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.wps.ai.helper.dto.StageDTO;
import lombok.Data;

import java.util.List;

/**
 * @program: wps-ai-helper
 * @description:
 * @author: zheng.huiyuan
 * @create: 2025-07-17 17:20
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QnaAnswerEvent {
    private String answer;            // 文本片段 / JSON (map类型)
    private String answerType;        // text | map
    private String reasoningContent;  // 思维链（可多条）
    private String bizType;           // map 时使用
    @JsonProperty("isEnd")
    @JSONField(name = "isEnd")
    private Integer isEnd;            // 0 / 1
    private String uuid;              // 分析步骤ID（可空）
    private Boolean nlpBegin;         // 写作专用
    private Boolean nlpEnd;           // 写作专用
    private List<StageDTO> stages;
}
