package com.trs.wps.ai.helper.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.wps.ai.helper.common.UpstreamException;
import com.trs.wps.ai.helper.config.LLMClientProperties;
import com.trs.wps.ai.helper.config.LLMTools;
import com.trs.wps.ai.helper.service.QaService;
import com.trs.wps.ai.helper.util.JsonUtils;
import com.trs.wps.ai.helper.vo.ChatRequestVO;
import com.trs.wps.ai.helper.vo.ChatResponseVO;
import com.trs.wps.ai.helper.vo.QnaAnswerEvent;
import com.trs.wps.ai.helper.vo.Varparam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: wps-ai-helper
 * @description:
 * @author: zheng.huiyuan
 * @create: 2025-07-17 15:27
 **/
@Slf4j
@Service
public class QaServiceImpl implements QaService {

    @Autowired
    private LLMClientProperties properties;

    @Autowired
    private  WebClient llmWebClient;


    /**
     * 设置认证头部信息
     * @param h HttpHeaders 对象
     */
    private void authHeaders(org.springframework.http.HttpHeaders h) {
        if (properties.getUsername() != null && !properties.getUsername().isEmpty()) {
            h.add("Username", properties.getUsername());
        }
        if (properties.getAccessToken() != null && !properties.getAccessToken().isEmpty()) {
            h.add("Access-Token", properties.getAccessToken());
        }
    }

    private List<Varparam> getVarparams(String content, String select){
        List<Varparam> varparams = new ArrayList<>();
        varparams.add(new Varparam("fullMarkdown", content));
        varparams.add(new Varparam("needOptimizeText", select));
        return  varparams;
    }

    @Override
    public Mono<ChatResponseVO.ChatData> sendQnaQuestion(String conversationId, String content, String select, String type) {
        ChatRequestVO req = ChatRequestVO.builder()
                .conversationId(conversationId)
                .tenantId(properties.getTenantId())
                .vars(getVarparams(content, select))
                .question(select)
                .build();

        String path = String.format("/llmops/chat/apps/%s/conversations/info", LLMTools.getToolsApp(type).getAppId());

        return llmWebClient.post()
                .uri( path)
                .contentType(MediaType.APPLICATION_JSON)
                .headers(this::authHeaders)
                .bodyValue(req)
                .retrieve()
                .bodyToMono(ChatResponseVO.class)
                .flatMap(resp -> {
                    if (resp.getCode() != 200 || resp.getData() == null || resp.getData().isEmpty()) {
                        return Mono.error(new UpstreamException(resp.getCode(), "sendRewriteQuestion failed"));
                    }
                    return Mono.just(resp.getData().get(0));
                });
    }

    @Override
    public Flux<QnaAnswerEvent> getStreamAnswer(ChatResponseVO.ChatData chatData, String type) {
        String path = String.format("/llmops/chat/apps/%s/conversations/%s/qas/%s/stream", LLMTools.getToolsApp(type).getAppId(), chatData.getConversationId(), chatData.getQaId());
        return llmWebClient.post()
                .uri(path)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .headers(this::authHeaders)
                .bodyValue(new ChatLogIdBody(chatData.getChatLogId()))
                .retrieve()
                .bodyToFlux(String.class)
                .mapNotNull(str -> {
                    String s = str.trim();
                    if (s.isEmpty()) {
                        return null;
                    }
                    try {
                        log.info("Received SSE line: {}", s);
                        return JSON.parseObject(s,QnaAnswerEvent.class);
                    } catch (Exception e) {
                        log.warn("Ignore non-JSON qna SSE line: {}", s);
                        return null;
                    }
                });


    }

    /** 内部 body 封装 chatLogId */
    private record ChatLogIdBody(Long chatLogId) {}
}
