package com.trs.wps.ai.helper.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 聊天请求参数
 *
 * <AUTHOR>
 * @create 2025-03-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatRequestVO {
    
    /**
     * 会话ID
     */
    private String conversationId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 问题内容
     */
    private String question;
    
    /**
     * 附件ID列表
     */
    private List<Long> appendixIds;

    private Long globalConversationId;

    private List<Varparam> vars;
}