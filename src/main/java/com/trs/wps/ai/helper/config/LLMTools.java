package com.trs.wps.ai.helper.config;

import com.trs.wps.ai.helper.vo.ToolsApp;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * @program: wps-ai-helper
 * @description:
 * @author: zheng.huiyuan
 * @create: 2025-07-17 16:29
 **/

@Data
@Configuration
@ConfigurationProperties(prefix = "llm")
public class LLMTools {
    private List<ToolsApp> tools;
    
    private static LLMTools instance;
    
    @PostConstruct
    public void init() {
        instance = this;
    }

    public static ToolsApp getToolsApp(String toolTitle) {
        if (instance == null || instance.tools == null) {
            return null;
        }
        return instance.tools.stream()
                .filter(tool -> toolTitle.equals(tool.getToolTitle()))
                .findFirst()
                .orElse(instance.tools.get(0));
    }
}
