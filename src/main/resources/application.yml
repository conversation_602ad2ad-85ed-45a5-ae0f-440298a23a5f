server:
  port: 8088
  servlet:
    context-path: /wpsAiHelper


logging:
  level:
    root: INFO
    org.springframework.web.reactive.function.client: DEBUG

helper:
  llm:
    base-url: "https://llmys.trscd.com.cn"        # 不含尾斜线
    tenant-id: "60"                        # 文档示例值；按实际
    username: "WPS-AI"            # 若登录后可不传
    access-token: "a38b67c9650548bb"           # 若登录后可不传
    connect-timeout-ms: 5000
    read-timeout-ms: 0                    # 0=无限（流式建议）

ai-open-service:
  base-url: "http://xiaosi.trs.cn"        # AI开放服务平台基础URL
  username: "18582861920"            # 登录用户名
  code: "$2a$10$PwjAGv4KVb5Q354dSfyZm.yYCh7kyR/7QETVFwEzEWdzC0edFKJMS"           # 安全码

llm:
  tools:
    - {"toolTitle": "抽取关键词", "appId": 1193}
    - {"toolTitle": "润色", "appId": 1191}
    - {"toolTitle": "续写", "appId": 1196}
    - {"toolTitle": "扩写", "appId": 1195}

tts:
  url: http://192.168.200.13:9881/tts